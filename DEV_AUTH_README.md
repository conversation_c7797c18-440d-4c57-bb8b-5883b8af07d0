# Development Authentication Setup

This document explains how to set up local development authentication to bypass the native app authentication flow.

## Overview

In production, the app waits for authentication data (token, userID, etc.) from the native React Native app via `window.postMessage`. For local development, you can bypass this waiting period by using hardcoded authentication values.

## Quick Start

### Option 1: Using the Helper Script (Recommended)

```bash
# Enable development authentication
node scripts/dev-auth.js enable

# Start your development server
npm run dev

# Check status
node scripts/dev-auth.js status

# Disable when you want to test native app integration
node scripts/dev-auth.js disable
```

### Option 2: Manual Environment Variables

Add these variables to your `.env` file:

```env
# Local Development Authentication
NEXT_PUBLIC_DEV_TOKEN=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJraWQiOiJqd3VRcWwzOFJIYzFyTHltY2M4RmpzZ2dBV0w3ck0yciIsInVpZCI6IngxNjkwNzlmZDczYTQ1MzNkNGZkNWU3OTA5NzEyMmE5YiIsImV4cCI6MTc1OTIwNDcwNH0.EZRGk3-m8VIdmFxW4WMUJU1Mjhi-P8aOQRfaeLoKVko
NEXT_PUBLIC_DEV_USER_ID=x169079fd73a4533d4fd5e79097122a9b
NEXT_PUBLIC_DEV_VERSION_APP_NAME=1.0.0
NEXT_PUBLIC_DEV_LOCALE=VN
```

## How It Works

1. **Development Mode**: When the environment variables are set, the `useNativeToken` hook immediately sets the authentication data without waiting for native app messages.

2. **Production Mode**: When the environment variables are not set, the app behaves normally - waiting for authentication from the native app.

3. **API Fallbacks**: All API routes have fallback tokens that match your development token, so API calls will work seamlessly.

## Authentication Data

- **Token**: `eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJraWQiOiJqd3VRcWwzOFJIYzFyTHltY2M4RmpzZ2dBV0w3ck0yciIsInVpZCI6IngxNjkwNzlmZDczYTQ1MzNkNGZkNWU3OTA5NzEyMmE5YiIsImV4cCI6MTc1OTIwNDcwNH0.EZRGk3-m8VIdmFxW4WMUJU1Mjhi-P8aOQRfaeLoKVko`
- **User ID**: `x169079fd73a4533d4fd5e79097122a9b`
- **Version**: `1.0.0`
- **Locale**: `VN`

## Development Workflow

1. **Start Development**: 
   ```bash
   node scripts/dev-auth.js enable
   npm run dev
   ```

2. **Develop Features**: The app will immediately proceed to the home screen without waiting for native authentication.

3. **Test Native Integration**: 
   ```bash
   node scripts/dev-auth.js disable
   npm run dev
   ```
   Now the app will wait for native authentication (useful for testing the actual integration).

4. **Check Current Mode**:
   ```bash
   node scripts/dev-auth.js status
   ```

## Console Output

When development mode is active, you'll see this message in the browser console:
```
🚀 Using development authentication data
```

## Files Modified

- `.env` - Contains development authentication variables
- `src/hooks/useNativeToken.ts` - Modified to check for development variables
- `src/app/api/*/route.ts` - Updated fallback tokens to match your token
- `scripts/dev-auth.js` - Helper script for toggling modes

## Security Notes

- Development tokens are only used in development mode
- The environment variables are prefixed with `NEXT_PUBLIC_` so they're available in the browser
- In production builds, these variables should not be set
- The fallback tokens in API routes provide a safety net for development

## Troubleshooting

### App Still Waiting for Authentication
- Check that environment variables are set: `node scripts/dev-auth.js status`
- Restart your development server after enabling dev auth
- Check browser console for the "🚀 Using development authentication data" message

### API Calls Failing
- Ensure your backend server is running and accessible
- Check that `BE_URL` and `ACCESS_KEY_BE` are correctly set in `.env`
- Verify the token hasn't expired (current token expires in 2025)

### Want to Test Native App Integration
- Disable dev auth: `node scripts/dev-auth.js disable`
- The app will then wait for authentication from the native app as normal
