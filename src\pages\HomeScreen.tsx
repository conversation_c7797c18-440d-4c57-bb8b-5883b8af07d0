// src/pages/HomeScreen.tsx
import React, { useEffect } from 'react'
import Image from 'next/image'
import { ScrollArea } from '@/components/ui/scroll-area'
import { AspectRatio } from '@/components/ui/aspect-ratio'
import Mute from '@/assets/home/<USER>'
import Unmute from '@/assets/home/<USER>'
import StageEntry from '@/components/homeScreen/StageEntry'
import RoundIconButton from '@/components/common/RoundIconButton'
import { Header } from '@/components/homeScreen/Header'
import { Footer } from '@/components/homeScreen/Footer'
import { stageConfig } from '@/lib/constants'
import OutOfTurnsDialog from '@/components/dialog/OutOfTurnsDialog'
import CheckInDialog from '@/components/dialog/CheckInDialog'
import MissionRewardDialog from '@/components/dialog/MissionRewardDialog'
import WinDialog from '@/components/dialog/WinDialog'
import { useHomeScreen } from '@/hooks/useHomeScreen'
import DailyMissionDrawer from '@/components/drawer/DailyMissionDrawer'
import LeaderboardDrawer from '@/components/drawer/LeaderboardDrawer'
import HistoryDrawer from '@/components/drawer/HistoryDrawer'
import GiftListDrawer from '@/components/drawer/GiftListDrawer'
import GuidelineDrawer from '@/components/drawer/GuidelineDrawer'
import { useShareDialogStore } from '@/stores/useShareDialogStore'
import ShareDialog from '@/components/dialog/ShareDialog'
import { PostMessageType } from '@/lib/postMessage'
import { useNativeStore } from '@/stores/useNativeStore'
import { useAudioStore } from '@/stores/useAudioStore'
import { useRankingGameCampaign } from '@/hooks/useRankingGameCampaign'

interface HomeScreenProps {
  onStageClick: (stage: number) => void
}

declare global {
  interface Window {
    startBackgroundMusic?: () => void
    pauseBackgroundMusic?: () => void
    resumeBackgroundMusic?: () => void
    checkMusicState?: () => { isMuted: boolean; isBackgroundMusicPlaying: boolean; hasUserInteracted: boolean }
  }
}

const HomeScreen: React.FC<HomeScreenProps> = ({ onStageClick }) => {
  const nativeData = useNativeStore()
  const { ensureMusicContinuity } = useAudioStore()
  const { refetch: refetchRanking } = useRankingGameCampaign(nativeData.userId)

  useEffect(() => {
    if (window.ReactNativeWebView?.postMessage) {
      window.ReactNativeWebView.postMessage(JSON.stringify({ type: PostMessageType.APP_LOADED }))
    }

    // Ensure background music continues when entering HomeScreen
    ensureMusicContinuity()
    refetchRanking()
  }, [ensureMusicContinuity, refetchRanking])

  const {
    viewportRef,
    stageRefs,
    muted,
    toggleMute,
    showOutOfTurns,
    setShowOutOfTurns,
    currentDialog,
    handleCloseDialog,
    winStage,
    setWinStage,
    getVariant,
    handleStageClick
  } = useHomeScreen(onStageClick)

  const [showDailyMission, setShowDailyMission] = React.useState(false)
  const [showLeaderboard, setShowLeaderboard] = React.useState(false)
  const [showHistory, setShowHistory] = React.useState(false)
  const [showGiftList, setShowGiftList] = React.useState(false)
  const [showGuideline, setShowGuideline] = React.useState(false)
  const { show, open, close, level } = useShareDialogStore()

  return (
    <div className='font-bdstreet relative h-[100dvh] w-[100dvw] overflow-hidden bg-white'>
      {/* Debug Display for Native App Data */}
      {/* <div className='absolute top-0 left-0 z-50 max-w-[90%] rounded-br-lg bg-black/80 p-2 text-xs text-white'>
        <div className='font-mono'>
          <div>Token: {nativeData.token || 'null'}</div>
          <div>Locale: {nativeData.locale || 'null'}</div>
          <div>UserId: {nativeData.userId || 'null'}</div>
          <div>Version: {nativeData.versionAppName || 'null'}</div>
          <div>Top: {nativeData.top || 'null'}</div>
          <div>Bottom: {nativeData.bottom || 'null'}</div>
          <div>Timeout: {nativeData.timeoutReached ? 'true' : 'false'}</div>
        </div>
      </div> */}

      <div className='absolute top-[12.5%] left-0 h-[76%] w-full'>
        <ScrollArea viewportRef={viewportRef} className='h-full w-full' overscroll='none'>
          <div className='relative min-h-full'>
            <Image src='/home/<USER>' alt='Home Background' fill priority quality={100} />
            <div className='relative z-10 flex min-h-full items-center justify-end text-white'>
              <AspectRatio ratio={390 / 1570} className='relative w-full'>
                {stageConfig.map(({ stage, position }) => (
                  <StageEntry
                    key={stage}
                    stage={stage}
                    position={position}
                    onClick={handleStageClick}
                    variant={getVariant(stage)}
                    ref={(el) => {
                      stageRefs.current[stage] = el
                    }}
                  />
                ))}
              </AspectRatio>
            </div>
          </div>
        </ScrollArea>
      </div>

      <Header onOpenGiftList={() => setShowGiftList(true)} />
      <RoundIconButton
        icon={muted ? <Mute className='z-20 h-[50%] w-[50%]' /> : <Unmute className='z-20 h-[50%] w-[50%]' />}
        onClick={toggleMute}
        className='absolute top-[17%] right-[2.5%]'
      />
      <Footer
        onStageClick={handleStageClick}
        onOpenDailyMission={() => setShowDailyMission(true)}
        onOpenLeaderboard={() => setShowLeaderboard(true)}
        onOpenHistory={() => setShowHistory(true)}
        onOpenGuideline={() => setShowGuideline(true)}
      />

      <OutOfTurnsDialog
        open={showOutOfTurns}
        onClose={() => {
          setShowOutOfTurns(false)
          setShowDailyMission(true)
        }}
      />
      <CheckInDialog open={currentDialog?.type === 'checkin'} onClose={handleCloseDialog} />

      <MissionRewardDialog
        open={['task', 'invite', 'share'].includes(currentDialog?.type ?? '')}
        onClose={handleCloseDialog}
        rewardCount={currentDialog?.rewardCount ?? 1}
      />

      <WinDialog
        open={winStage !== null}
        onClose={() => setWinStage(null)}
        stage={winStage ?? 0}
        variant='home'
        onNavigate={onStageClick}
        onShare={(stage) => open(stage)} // capture correct level
      />

      <ShareDialog open={show} onClose={close} level={level ?? 0} />

      <DailyMissionDrawer open={showDailyMission} onClose={() => setShowDailyMission(false)} />
      <LeaderboardDrawer open={showLeaderboard} onClose={() => setShowLeaderboard(false)} />
      <HistoryDrawer open={showHistory} onClose={() => setShowHistory(false)} />
      <GiftListDrawer open={showGiftList} onClose={() => setShowGiftList(false)} />
      <GuidelineDrawer open={showGuideline} onClose={() => setShowGuideline(false)} />
    </div>
  )
}

export default HomeScreen
